"""
In-Depth Report Review Agent
---------------------------
Interactive agent for reviewing in-depth research reports with continue/stop options.
"""

import logging
import json

from models.schema import PerplexityReport

logger = logging.getLogger(__name__)


class ReportReviewAgent:
    """
    Interactive agent for reviewing in-depth research reports.
    
    This agent allows users to review generated in-depth research reports and either:
    1. Continue with the current report
    2. Stop the pipeline (for manual editing)
    
    Note: This agent does not provide LLM-based editing since in-depth reports 
    contain large amounts of data that are better reviewed and edited manually.
    """
    
    def __init__(self):
        """
        Initialize the Report Review Agent.
        """
        logger.info("ReportReviewAgent initialized")
    
    def interactive_review(self, perplexity_report: PerplexityReport, title: str) -> bool:
        """
        Run an interactive review session for the in-depth research report.
        
        Args:
            perplexity_report (PerplexityReport): The generated research report
            title (str): The title of the story/incident
            
        Returns:
            bool: True to continue with the pipeline, False to stop for manual editing
        """
        while True:
            # Display the report summary
            self.display_report_summary(perplexity_report, title)
            
            # Ask the user what they want to do
            print("\nIN-DEPTH REPORT REVIEW OPTIONS:")
            print("1. Continue with this report")
            print("2. Stop for manual editing")
            print("3. View full report content")
            print("4. View citations")
            
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                logger.info("User chose to continue with the current report")
                return True
            
            elif choice == "2":
                logger.info("User chose to stop for manual editing")
                print("\nPipeline stopped for manual editing.")
                print("You can manually edit the report.json file and then use the --continue flag to resume.")
                return False
            
            elif choice == "3":
                self.display_full_report(perplexity_report)
                # Continue the loop to show options again
            
            elif choice == "4":
                self.display_citations(perplexity_report)
                # Continue the loop to show options again
            
            else:
                print("Invalid choice. Please enter 1, 2, 3, or 4.")
    
    def display_report_summary(self, perplexity_report: PerplexityReport, title: str):
        """
        Display a summary of the research report to the user.
        
        Args:
            perplexity_report (PerplexityReport): The research report
            title (str): The title of the story/incident
        """
        print("\n" + "="*80)
        print(f"IN-DEPTH RESEARCH REPORT FOR: {title}")
        print("="*80)
        
        # Display basic information
        print(f"Query: {perplexity_report.query[:200]}{'...' if len(perplexity_report.query) > 200 else ''}")
        print(f"Report Length: {len(perplexity_report.report_content)} characters")
        print(f"Number of Citations: {len(perplexity_report.citations)}")
        
        # Display usage stats if available
        if perplexity_report.usage_stats:
            print(f"Usage Stats: {json.dumps(perplexity_report.usage_stats, indent=2)}")
        
        # Display first few lines of the report content
        lines = perplexity_report.report_content.split('\n')
        preview_lines = lines[:10]  # Show first 10 lines
        
        print("\nREPORT PREVIEW (first 10 lines):")
        print("-" * 40)
        for line in preview_lines:
            print(line)
        
        if len(lines) > 10:
            print(f"... and {len(lines) - 10} more lines")
        
        print("="*80)
    
    def display_full_report(self, perplexity_report: PerplexityReport):
        """
        Display the full research report content.
        
        Args:
            perplexity_report (PerplexityReport): The research report
        """
        print("\n" + "="*80)
        print("FULL RESEARCH REPORT CONTENT")
        print("="*80)
        print(perplexity_report.report_content)
        print("="*80)
        
        input("\nPress Enter to continue...")
    
    def display_citations(self, perplexity_report: PerplexityReport):
        """
        Display the citations from the research report.
        
        Args:
            perplexity_report (PerplexityReport): The research report
        """
        print("\n" + "="*80)
        print("RESEARCH REPORT CITATIONS")
        print("="*80)
        
        if not perplexity_report.citations:
            print("No citations available.")
        else:
            for i, citation in enumerate(perplexity_report.citations, 1):
                print(f"\n{i}. {json.dumps(citation, indent=2, ensure_ascii=False)}")
        
        print("="*80)
        
        input("\nPress Enter to continue...")
