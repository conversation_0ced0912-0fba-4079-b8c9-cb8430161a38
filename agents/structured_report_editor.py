"""
Structured Report Editing Agent
------------------------------
Interactive agent for editing specific keys in structured reports.
"""

import logging
import json
from typing import List, Union

from crewai import Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent
from models.schema import StructuredReport, PerplexityReport

logger = logging.getLogger(__name__)


class StructuredReportEditingAgent:
    """
    Interactive agent for editing structured reports.
    
    This agent allows users to review structured reports and edit specific keys
    using LLM assistance. The agent receives full context but returns only the
    updated key value.
    """
    
    # Available keys that can be edited
    EDITABLE_KEYS = [
        'title',
        'executive_summary',
        'background_context',
        'key_events_timeline',
        'main_participants',
        'detailed_analysis',
        'multiple_perspectives',
        'impact_and_consequences',
        'cultural_social_context',
        'lessons_learned',
        'verified_sources',
        'factual_accuracy_notes'
    ]
    
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the Structured Report Editing Agent.
        
        Args:
            verbose (bool): Whether to enable verbose output
            model (str): The LLM model to use for editing
            provider (str): The LLM provider to use for editing
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        logger.info(f"StructuredReportEditingAgent initialized with model: {model}, provider: {provider}")
    
    def interactive_edit(self, structured_report: StructuredReport, perplexity_report: PerplexityReport, structured_report_json_path: str = None) -> StructuredReport:
        """
        Run an interactive editing session for the structured report.

        Args:
            structured_report (StructuredReport): The structured report to edit
            perplexity_report (PerplexityReport): The original in-depth report for context
            structured_report_json_path (str, optional): Path to save the structured report JSON file after each edit.
                                                        If None, the report will not be saved during editing.

        Returns:
            StructuredReport: The edited structured report
        """
        current_report = structured_report
        
        while True:
            # Display the current report
            self.display_report_summary(current_report)
            
            # Ask the user what they want to do
            print("\nSTRUCTURED REPORT EDITING OPTIONS:")
            print("1. Continue with this report")
            print("2. Edit a specific field")
            print("3. View full report")
            print("4. View specific field in detail")
            
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                logger.info("User chose to continue with the current report")
                return current_report
            
            elif choice == "2":
                # Edit a specific field
                current_report = self.edit_field(current_report, perplexity_report, structured_report_json_path)
            
            elif choice == "3":
                self.display_full_report(current_report)
            
            elif choice == "4":
                self.view_specific_field(current_report)
            
            else:
                print("Invalid choice. Please enter 1, 2, 3, or 4.")

    def save_structured_report_to_json(self, structured_report: StructuredReport, structured_report_json_path: str) -> None:
        """
        Save the structured report to a JSON file.

        Args:
            structured_report (StructuredReport): The structured report to save
            structured_report_json_path (str): Path to the JSON file
        """
        try:
            with open(structured_report_json_path, 'w', encoding='utf-8') as f:
                json.dump(structured_report.model_dump(), f, ensure_ascii=False, indent=4)

            logger.info(f"Structured report saved to {structured_report_json_path}")

        except Exception as e:
            logger.error(f"Error saving structured report to {structured_report_json_path}: {str(e)}")
            print(f"\nError saving structured report: {str(e)}")

    def display_report_summary(self, structured_report: StructuredReport):
        """
        Display a summary of the structured report.
        
        Args:
            structured_report (StructuredReport): The structured report
        """
        print("\n" + "="*80)
        print(f"STRUCTURED REPORT: {structured_report.title}")
        print("="*80)
        
        # Display field summaries
        for key in self.EDITABLE_KEYS:
            value = getattr(structured_report, key)
            if isinstance(value, str):
                preview = value[:100] + "..." if len(value) > 100 else value
                print(f"{key}: {preview}")
            elif isinstance(value, list):
                print(f"{key}: [{len(value)} items] {', '.join(str(v)[:50] for v in value[:3])}{'...' if len(value) > 3 else ''}")
        
        print("="*80)
    
    def display_full_report(self, structured_report: StructuredReport):
        """
        Display the full structured report.
        
        Args:
            structured_report (StructuredReport): The structured report
        """
        print("\n" + "="*80)
        print("FULL STRUCTURED REPORT")
        print("="*80)
        
        report_dict = structured_report.model_dump()
        print(json.dumps(report_dict, indent=2, ensure_ascii=False))
        
        print("="*80)
        input("\nPress Enter to continue...")
    
    def view_specific_field(self, structured_report: StructuredReport):
        """
        View a specific field in detail.
        
        Args:
            structured_report (StructuredReport): The structured report
        """
        print("\nAvailable fields:")
        for i, key in enumerate(self.EDITABLE_KEYS, 1):
            print(f"{i}. {key}")
        
        try:
            field_choice = int(input("\nEnter field number to view: ").strip()) - 1
            if 0 <= field_choice < len(self.EDITABLE_KEYS):
                key = self.EDITABLE_KEYS[field_choice]
                value = getattr(structured_report, key)
                
                print(f"\n{key.upper()}:")
                print("-" * 40)
                if isinstance(value, list):
                    for i, item in enumerate(value, 1):
                        print(f"{i}. {item}")
                else:
                    print(value)
                print("-" * 40)
                input("\nPress Enter to continue...")
            else:
                print("Invalid field number.")
        except ValueError:
            print("Invalid input. Please enter a number.")
    
    def edit_field(self, structured_report: StructuredReport, perplexity_report: PerplexityReport, structured_report_json_path: str = None) -> StructuredReport:
        """
        Edit a specific field in the structured report.

        Args:
            structured_report (StructuredReport): The current structured report
            perplexity_report (PerplexityReport): The original in-depth report for context
            structured_report_json_path (str, optional): Path to save the structured report JSON file after edit.
                                                        If None, the report will not be saved during editing.

        Returns:
            StructuredReport: The updated structured report
        """
        print("\nAvailable fields to edit:")
        for i, key in enumerate(self.EDITABLE_KEYS, 1):
            print(f"{i}. {key}")
        
        try:
            field_choice = int(input("\nEnter field number to edit: ").strip()) - 1
            if 0 <= field_choice < len(self.EDITABLE_KEYS):
                key = self.EDITABLE_KEYS[field_choice]
                
                # Show current value
                current_value = getattr(structured_report, key)
                print(f"\nCurrent value for '{key}':")
                print("-" * 40)
                if isinstance(current_value, list):
                    for i, item in enumerate(current_value, 1):
                        print(f"{i}. {item}")
                else:
                    print(current_value)
                print("-" * 40)
                
                # Get editing instructions
                edit_prompt = input(f"\nEnter your editing instructions for '{key}': ").strip()
                
                if not edit_prompt:
                    print("No editing instructions provided.")
                    return structured_report
                
                print(f"\nUpdating '{key}' based on your instructions. This may take a moment...")
                
                # Edit the field using LLM
                new_value = self.edit_field_with_llm(key, current_value, edit_prompt, structured_report, perplexity_report)
                
                # Update the structured report
                updated_report_dict = structured_report.model_dump()
                updated_report_dict[key] = new_value
                
                updated_report = StructuredReport(**updated_report_dict)
                print(f"\nField '{key}' has been updated.")

                # Save the structured report to JSON after the edit
                if structured_report_json_path:
                    self.save_structured_report_to_json(updated_report, structured_report_json_path)
                    print(f"Structured report updated in {structured_report_json_path}")

                return updated_report
            else:
                print("Invalid field number.")
                return structured_report
        except ValueError:
            print("Invalid input. Please enter a number.")
            return structured_report

    def edit_field_with_llm(self, field_key: str, current_value: Union[str, List[str]],
                           edit_prompt: str, structured_report: StructuredReport,
                           perplexity_report: PerplexityReport) -> Union[str, List[str]]:
        """
        Edit a specific field using LLM assistance.

        Args:
            field_key (str): The key of the field being edited
            current_value (Union[str, List[str]]): The current value of the field
            edit_prompt (str): User's instructions for editing
            structured_report (StructuredReport): The full structured report for context
            perplexity_report (PerplexityReport): The original in-depth report for context

        Returns:
            Union[str, List[str]]: The updated field value
        """
        logger.info(f"Editing field '{field_key}' using LLM")

        # Create the field editor agent
        field_editor = create_rate_limited_agent(
            role="Structured Report Field Editor",
            goal=f"Edit the '{field_key}' field in a structured research report based on user instructions",
            backstory=f"""You are an expert editor specializing in structured research reports for documentary narration.
            You have deep knowledge of how to organize and present information for Hindi documentary-style content.

            You excel at:
            - Maintaining factual accuracy and proper sourcing
            - Organizing information for documentary narration
            - Ensuring content flows well for audio presentation
            - Preserving the documentary style and tone
            - Working with structured data formats""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )

        # Determine the expected output format based on field type
        is_list_field = isinstance(current_value, list)

        if is_list_field:
            format_instruction = "Return ONLY a JSON array of strings (e.g., [\"item1\", \"item2\", \"item3\"]). Do not include any other text or explanation."
        else:
            format_instruction = "Return ONLY the updated text content. Do not include any JSON formatting, quotes, or other markup."

        # Create the editing task
        editing_task = Task(
            description=f"""Edit the '{field_key}' field in this structured research report based on the user's instructions.

FIELD TO EDIT: {field_key}

CURRENT VALUE:
{json.dumps(current_value, indent=2, ensure_ascii=False) if is_list_field else current_value}

USER'S EDITING INSTRUCTIONS:
{edit_prompt}

FULL STRUCTURED REPORT CONTEXT:
{json.dumps(structured_report.model_dump(), indent=2, ensure_ascii=False)}

ORIGINAL IN-DEPTH REPORT CONTEXT:
Title: {structured_report.title}
Query: {perplexity_report.query}
Report Content: {perplexity_report.report_content[:2000]}{'...' if len(perplexity_report.report_content) > 2000 else ''}

Your task is to update ONLY the '{field_key}' field based on the user's instructions while:
1. Maintaining factual accuracy based on the original research
2. Ensuring the content is suitable for Hindi documentary narration
3. Preserving the appropriate tone and style
4. Following the user's specific editing requests

{format_instruction}""",
            agent=field_editor,
            expected_output=f"The updated value for the '{field_key}' field, formatted as {'a JSON array' if is_list_field else 'plain text'}."
        )

        # Execute the task
        crew = Crew(
            agents=[field_editor],
            tasks=[editing_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        try:
            result = crew.kickoff()

            # Extract the result
            if hasattr(result, 'raw'):
                edited_content = result.raw.strip()
            else:
                edited_content = str(result).strip()

            # Parse the result based on field type
            if is_list_field:
                try:
                    # Try to parse as JSON array
                    new_value = json.loads(edited_content)
                    if not isinstance(new_value, list):
                        raise ValueError("Expected a list")
                    logger.info(f"Successfully edited list field '{field_key}'")
                    return new_value
                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"Error parsing list field result: {e}")
                    print(f"Error parsing the edited content as a list. Returning original value.")
                    return current_value
            else:
                logger.info(f"Successfully edited text field '{field_key}'")
                return edited_content

        except Exception as e:
            logger.error(f"Error editing field '{field_key}': {str(e)}")
            print(f"Error editing field: {str(e)}")
            print("Returning original value.")
            return current_value
