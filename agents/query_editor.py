"""
Query Editing Agent
------------------
Interactive agent for reviewing and editing research queries before generating in-depth reports.
"""

import logging

from crewai import Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent

logger = logging.getLogger(__name__)


class QueryEditingAgent:
    """
    Interactive agent for reviewing and editing research queries.
    
    This agent allows users to review generated research queries and either:
    1. Continue with the current query
    2. Edit the query using LLM assistance with user prompts
    """
    
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the Query Editing Agent.
        
        Args:
            verbose (bool): Whether to enable verbose output
            model (str): The LLM model to use for query editing
            provider (str): The LLM provider to use for query editing
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        logger.info(f"QueryEditingAgent initialized with model: {model}, provider: {provider}")
    
    def interactive_edit(self, original_query: str, title: str, context: str = "", query_file_path: str = None) -> str:
        """
        Run an interactive editing session for the research query.

        Args:
            original_query (str): The original generated research query
            title (str): The title of the story/incident
            context (str): Optional additional context about the story/incident
            query_file_path (str): Optional path to the query file to update after each edit

        Returns:
            str: The final query (either original or edited)
        """
        current_query = original_query
        
        while True:
            # Display the current query
            self.display_query(current_query, title)
            
            # Ask the user what they want to do
            print("\nQUERY REVIEW OPTIONS:")
            print("1. Continue with this query")
            print("2. Edit the query")
            
            choice = input("\nEnter your choice (1-2): ").strip()
            
            if choice == "1":
                logger.info("User chose to continue with the current query")
                return current_query
            
            elif choice == "2":
                # Get user's editing instructions
                edit_prompt = input("\nEnter your editing instructions (what changes you want to make to the query): ").strip()
                
                if not edit_prompt:
                    print("No editing instructions provided. Please try again.")
                    continue
                
                print("\nGenerating edited query based on your instructions. This may take a moment...")
                
                # Edit the query using LLM
                current_query = self.edit_query(current_query, edit_prompt, title, context)
                print("\nQuery has been updated based on your instructions.")

                # Update the query file if path is provided
                if query_file_path:
                    self.save_query_to_file(query_file_path, current_query, title, context, is_edited=True)
            
            else:
                print("Invalid choice. Please enter 1 or 2.")
    
    def display_query(self, query: str, title: str):
        """
        Display the current research query to the user.
        
        Args:
            query (str): The research query to display
            title (str): The title of the story/incident
        """
        print("\n" + "="*80)
        print(f"RESEARCH QUERY FOR: {title}")
        print("="*80)
        print(query)
        print("="*80)
    
    def edit_query(self, original_query: str, edit_prompt: str, title: str, context: str = "") -> str:
        """
        Edit the research query based on user instructions using LLM.
        
        Args:
            original_query (str): The original research query
            edit_prompt (str): User's instructions for editing the query
            title (str): The title of the story/incident
            context (str): Optional additional context about the story/incident
            
        Returns:
            str: The edited research query
        """
        logger.info("Editing research query based on user instructions")
        
        # Create the query editor agent
        query_editor = create_rate_limited_agent(
            role="Research Query Editor",
            goal="Edit and improve research queries based on user feedback and instructions",
            backstory="""You are an expert research query editor with deep knowledge of how to structure 
            comprehensive research queries for AI research tools like Perplexity AI Sonar. You understand 
            how to optimize queries to gather thorough, factual, and well-sourced research reports.
            
            You excel at:
            - Refining query structure and clarity
            - Adding or removing specific research angles
            - Improving question formulation for better research outcomes
            - Maintaining focus on factual accuracy and comprehensive coverage
            - Optimizing queries for AI research tools""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )
        
        # Create the query editing task
        editing_task = Task(
            description=f"""Edit the following research query based on the user's specific instructions:

ORIGINAL TITLE: {title}
{f"ADDITIONAL CONTEXT: {context}" if context else ""}

CURRENT RESEARCH QUERY:
{original_query}

USER'S EDITING INSTRUCTIONS:
{edit_prompt}

Your task is to generate a completely new and improved research query that incorporates the user's 
requested changes while maintaining the comprehensive structure needed for Perplexity AI Sonar.

The edited query should:
1. Address all the user's specific requests and changes
2. Maintain comprehensive coverage of the research topic
3. Be optimized for generating thorough, factual research reports
4. Include multiple research angles and specific questions
5. Be structured to work effectively with AI research tools

Generate the complete edited research query (not just the changes).""",
            agent=query_editor,
            expected_output="A complete, comprehensive research query that incorporates the user's requested changes while maintaining optimal structure for Perplexity AI Sonar research."
        )
        
        # Execute the task
        crew = Crew(
            agents=[query_editor],
            tasks=[editing_task],
            process=Process.sequential,
            verbose=self.verbose
        )
        
        try:
            result = crew.kickoff()
            
            # Extract the edited query from the result
            if hasattr(result, 'raw'):
                edited_query = result.raw
            else:
                edited_query = str(result)
            
            logger.info("Research query edited successfully")
            return edited_query.strip()
            
        except Exception as e:
            logger.error(f"Error editing research query: {str(e)}")
            print(f"\nError editing query: {str(e)}")
            print("Returning original query.")
            return original_query

    def save_query_to_file(self, file_path: str, query_text: str, title: str, context: str = "", is_edited: bool = False):
        """
        Save the research query to a markdown file.

        Args:
            file_path (str): Path to the markdown file to save
            query_text (str): The query text to save
            title (str): The story title
            context (str): Optional context
            is_edited (bool): Whether this is an edited version of the query
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"# Research Query\n\n")
                f.write(f"**Title:** {title}\n\n")
                if context:
                    f.write(f"**Context:** {context}\n\n")
                if is_edited:
                    f.write(f"**Generated Query (Edited):**\n\n{query_text}\n")
                else:
                    f.write(f"**Generated Query:**\n\n{query_text}\n")

            logger.info(f"Query saved to file: {file_path}")

        except Exception as e:
            logger.error(f"Error saving query to file {file_path}: {str(e)}")
            raise
