"""
Shorts Splitter Agent
--------------------
Splits each short's narration into segments optimized for text-to-video generation.
"""

import os
import json
import logging
from typing import List

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process

from models.schema import Short, ShortSegment
from utils.parsers import ShortSegmentListParser

logger = logging.getLogger(__name__)


class ShortsSplitterAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the shorts splitter agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key:
            raise ValueError("Missing required API key for ShortsSplitterAgent")

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.3,  # Lower temperature for more consistent splitting
            api_key=self.openai_api_key
        )

    def split_short(self, short: Short) -> List[ShortSegment]:
        """
        Split a short's narration into segments optimized for text-to-video generation.

        Args:
            short (Short): The short to split into segments

        Returns:
            List[ShortSegment]: List of segments for this short
        """
        logger.info(f"Splitting short {short.short_number} into text-to-video segments")

        # Create a parser for the list of ShortSegment models
        parser = ShortSegmentListParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the splitter agent
        splitter = Agent(
            role="Text-to-Video Segmentation Specialist",
            goal="Split Hindi short narration into optimal segments for text-to-video generation with precise timing control",
            backstory="""You are an expert in text-to-video content optimization with deep understanding of 
            AI video generation constraints and capabilities. You specialize in breaking down narration into 
            segments that align perfectly with text-to-video model capabilities (5, 6, or 10 second durations) 
            while maintaining narrative flow and visual coherence. You understand how to create segments that 
            work well for automated video generation and maintain viewer engagement throughout short-form content.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        # Create the splitting task for this specific short
        splitting_task = Task(
            description=f"""
            Split the following Hindi short narration into optimal segments for text-to-video generation:

            SHORT DETAILS:
            Short Number: {short.short_number}
            Title: {short.title}
            Total Estimated Duration: {short.estimated_duration_seconds} seconds
            Narration: {short.narration}

            Your task is to:

            1. **Precise Duration Targeting**: Split the narration into segments with exactly these durations:
               - 5 seconds: For quick, punchy statements or dramatic reveals
               - 10 seconds: For complex explanations or emotional moments that need more time
               
               Choose the duration based on content complexity and pacing needs.

            2. **Text-to-Video Optimization**: Ensure each segment:
               - Contains complete thoughts or phrases (no mid-sentence cuts)
               - Has clear visual storytelling potential
               - Maintains narrative coherence when converted to video
               - Works well with automated video generation systems

            3. **Natural Break Points**: Split at logical points such as:
               - End of complete sentences or thoughts
               - Natural pauses in speech
               - Transition points in the narrative
               - Before or after dramatic moments

            4. **Segment Quality**: Each segment should:
               - Be self-contained enough for video generation
               - Maintain the Hindi language style with natural English terms
               - Have appropriate pacing for the chosen duration
               - Support visual storytelling elements

            5. **Sequential Numbering**: Number segments sequentially starting from 1 within this short.

            6. **Duration Distribution**: Aim for a good mix of segment lengths:
               - Use 6-second segments as the default
               - Use 5-second segments for quick, impactful moments
               - Use 10-second segments sparingly, only when content requires it

            IMPORTANT GUIDELINES:
            - Preserve the original Hindi narration text exactly as provided
            - Do not modify, translate, or alter the language mixing
            - Ensure segments align with natural speech patterns
            - Consider how each segment will work as a standalone video clip
            - Maintain the documentary-style narrative voice
            - Total segment durations should approximately match the original short duration

            The segments will be used for automated text-to-video generation, so precision in timing 
            and content structure is crucial.
            """,
            agent=splitter,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[splitting_task],
            agents=[splitter],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        segments = ShortSegmentListParser.parse_output(parser, result)

        # If parsing fails, log the error and exit
        if segments is None:
            logger.error("Could not parse ShortsSplitter result for short %d, Raw output: %s", short.short_number, result)
            raise ValueError(f"Failed to parse segments from ShortsSplitter Agent for short {short.short_number}")

        # Validate segment durations
        valid_durations = {5, 6, 10}
        for segment in segments:
            if segment.estimated_duration_seconds not in valid_durations:
                logger.warning(f"Segment {segment.segment_number} has invalid duration {segment.estimated_duration_seconds}s, adjusting to 6s")
                segment.estimated_duration_seconds = 6

        # Log detailed information about the segments for this short
        total_duration = sum(segment.estimated_duration_seconds for segment in segments)
        logger.info(f"Short {short.short_number} splitting completed successfully:")
        logger.info(f"  - Created {len(segments)} segments")
        logger.info(f"  - Total duration: {total_duration} seconds (original: {short.estimated_duration_seconds}s)")
        
        # Log segment breakdown
        duration_counts = {5: 0, 6: 0, 10: 0}
        for segment in segments:
            duration_counts[segment.estimated_duration_seconds] += 1
        
        logger.info(f"  - Segment breakdown: {duration_counts[5]}x5s, {duration_counts[6]}x6s, {duration_counts[10]}x10s")

        return segments

    def save_segments_to_json(self, segments: List[ShortSegment], short_dir: str) -> None:
        """
        Save the segments to a JSON file in the short's directory.

        Args:
            segments (List[ShortSegment]): List of segments to save
            short_dir (str): Path to the short's directory
        """
        segments_json_path = os.path.join(short_dir, "segments.json")
        
        try:
            segments_data = [segment.model_dump() for segment in segments]
            with open(segments_json_path, 'w', encoding='utf-8') as f:
                json.dump(segments_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Segments saved to {segments_json_path}")
            
        except Exception as e:
            logger.error(f"Error saving segments to {segments_json_path}: {str(e)}")
            raise

    def process_all_shorts(self, story_dir: str) -> None:
        """
        Process all shorts in the story directory, splitting each into segments.

        Args:
            story_dir (str): Path to the main story directory containing short subdirectories
        """
        logger.info("Processing all shorts for segment splitting")

        # Find all short directories
        short_dirs = []
        for item in os.listdir(story_dir):
            item_path = os.path.join(story_dir, item)
            if os.path.isdir(item_path) and item.startswith("short-"):
                short_dirs.append(item_path)

        # Sort directories by short number
        short_dirs.sort(key=lambda x: int(x.split("short-")[-1]))

        if not short_dirs:
            logger.warning("No short directories found in story directory")
            return

        logger.info(f"Found {len(short_dirs)} shorts to process")

        # Process each short
        for short_dir in short_dirs:
            short_json_path = os.path.join(short_dir, "short.json")
            
            if not os.path.exists(short_json_path):
                logger.warning(f"Short JSON not found: {short_json_path}")
                continue

            try:
                # Load the short data
                with open(short_json_path, 'r', encoding='utf-8') as f:
                    short_data = json.load(f)
                    short = Short(**short_data)

                # Check if segments already exist
                segments_json_path = os.path.join(short_dir, "segments.json")
                if os.path.exists(segments_json_path):
                    logger.info(f"Segments already exist for short {short.short_number}, skipping")
                    continue

                # Split the short into segments
                segments = self.split_short(short)

                # Save segments to JSON
                self.save_segments_to_json(segments, short_dir)

                logger.info(f"Successfully processed short {short.short_number}")

            except Exception as e:
                logger.error(f"Error processing short in {short_dir}: {str(e)}")
                continue

        logger.info("Completed processing all shorts for segment splitting")
