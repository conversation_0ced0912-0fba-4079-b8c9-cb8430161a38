"""
Dialogue Splitter Agent
----------------------
Parses the Hindi story JSON to extract scene dialogues/cues.
"""

import os
import logging
from typing import List

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process

from models.schema import Story, SceneSegment
from utils.parsers import SceneSegmentListParser

logger = logging.getLogger(__name__)

class DialogueSplitterAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the dialogue splitter agent with necessary API keys.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key:
            raise ValueError("Missing required API key for DialogueSplitterAgent")

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.3,  # Lower temperature for more precise splitting
            api_key=self.openai_api_key
        )

    def split(self, story_data: Story) -> List[SceneSegment]:
        """
        Split the story into dialogue segments for narration.

        Args:
            story_data (Story): The story data with scenes

        Returns:
            List[SceneSegment]: List of scene segments with dialogue and timing information
        """
        logger.info("Starting dialogue splitting")

        all_segments = []

        # Process each scene individually
        for scene in story_data.scenes:
            logger.info(f"Processing scene {scene.scene_number}")
            scene_segments = self._split_scene(scene)
            all_segments.extend(scene_segments)

        logger.info(f"Generated {len(all_segments)} total segments from {len(story_data.scenes)} scenes")
        return all_segments

    def _split_scene(self, scene) -> List[SceneSegment]:
        """
        Split a single scene's narration into segments.

        Args:
            scene: The scene to split

        Returns:
            List[SceneSegment]: List of segments for this scene
        """
        # Create a parser for the list of SceneSegment models
        parser = SceneSegmentListParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the splitter agent
        splitter = Agent(
            role="Dialogue Segmentation Specialist",
            goal="Split Hindi narration into optimal segments for TTS",
            backstory="""You are an expert in audio storytelling. Your task is to
            split Hindi narration into optimal segments for text-to-speech conversion.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        # Create the splitting task for this specific scene
        splitting_task = Task(
            description=f"""
            Split the following scene narration (in modern conversational Hindi with English terms) into optimal segments for audio narration:

            Scene {scene.scene_number}:
            Narration: {scene.narration}

            Your task is to:

            1. Split the narration into smaller segments (5-8 seconds each when spoken)
            2. Each segment should be a natural pause point in the narration
            3. Maintain the flow and coherence of the story
            4. Ensure segments are not too short (minimum 3-4 seconds) or too long (maximum 10 seconds)

            IMPORTANT:
            - The narration uses modern conversational Hindi with naturally incorporated English terms - do not translate or modify the language mixing.
            - Keep the original text exactly as provided, only split it into logical segments.
            - Each segment should have scene_number: {scene.scene_number} and sequential segment_number starting from 1.
            """,
            agent=splitter,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[splitting_task],
            agents=[splitter],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        segments = SceneSegmentListParser.parse_output(parser, result)

        # If parsing fails, log the error and exit
        if segments is None:
            logger.warning("Could not parse DialogueSplitter result for scene %d, Raw output: %s", scene.scene_number, result)
            logger.warning("Please retry the task. Exiting...")
            exit(1)

        # Log detailed information about the segments for this scene
        logger.info(f"Scene {scene.scene_number} splitting completed successfully, created {len(segments)} segments")
        for i, segment in enumerate(segments):
            logger.info(f"Scene {scene.scene_number} Segment {i+1}: Segment {segment.segment_number}")

        return segments
