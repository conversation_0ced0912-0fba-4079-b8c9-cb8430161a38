"""
Editor Agent
-----------
Structures Perplexity AI research reports into organized, documentary-ready format.
"""

import json
import logging

from crewai import Agent, Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent
from utils.parsers import StructuredReportParser
from models.schema import PerplexityReport, StructuredReport

logger = logging.getLogger(__name__)

class EditorAgent:
    """
    Agent for structuring Perplexity AI research reports into organized, documentary-ready format.

    This agent takes raw research reports from Perplexity AI Sonar and structures them
    into a comprehensive format suitable for creating Hindi documentary-style narrations.
    """

    def __init__(self,
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai") -> None:
        """
        Initialize the Editor Agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI. Defaults to False.
            model (str): The model to use for the agent. Defaults to "gpt-4o-mini".
            provider (str): The LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

        logger.info(f"EditorAgent initialized with model: {model}, provider: {provider}")

    def structure_report(self,
                        perplexity_report: PerplexityReport,
                        title: str) -> StructuredReport:
        """
        Structure a Perplexity AI research report into organized, documentary-ready format.

        Args:
            perplexity_report (PerplexityReport): The raw report from Perplexity AI Sonar
            title (str): The title of the incident/event being reported

        Returns:
            StructuredReport: Structured research report ready for documentary narration
        """
        logger.info(f"Structuring Perplexity AI report for: {title}")

        # Create a parser for the StructuredReport model
        parser = StructuredReportParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the report structuring agent
        editor = create_rate_limited_agent(
            role="Research Report Structuring Specialist",
            goal="Structure comprehensive research reports into organized, documentary-ready format for Hindi narration",
            backstory="""You are an expert research report editor and documentary content specialist with extensive
            experience in organizing complex research data into clear, structured formats suitable for storytelling.

            Your expertise includes:
            - Analyzing comprehensive research reports and extracting key information
            - Organizing information into logical, narrative-friendly structures
            - Identifying key events, participants, and timelines from research data
            - Structuring content for documentary-style presentations
            - Ensuring factual accuracy and proper source attribution
            - Creating clear executive summaries and detailed analyses
            - Organizing multiple perspectives and viewpoints
            - Highlighting cultural and social contexts
            - Extracting lessons learned and long-term impacts

            You specialize in preparing research content for Hindi documentary narrations that are:
            - Factually accurate and well-sourced
            - Engaging and accessible to general audiences
            - Culturally sensitive and contextually appropriate
            - Structured for clear narrative flow
            - Educational and informative

            Your structured reports serve as the foundation for compelling documentary-style stories.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )

        # Extract the report content from Perplexity response
        report_content = perplexity_report.report_content
        citations = perplexity_report.citations

        # Create the structuring task
        task_description = f"""
        Structure the following comprehensive research report into an organized, documentary-ready format:

        TITLE: {title}

        RESEARCH REPORT CONTENT:
        {report_content}

        CITATIONS AND SOURCES:
        {json.dumps(citations, indent=2)}

        Your task is to analyze this research report and structure it into a comprehensive format suitable for creating
        a Hindi documentary-style narration. You must:

        1. **Executive Summary**: Create a brief, compelling summary of the incident/event that captures the essence
           and significance of the story.

        2. **Background Context**: Extract and organize the historical, cultural, and contextual background information
           that helps understand the incident/event.

        3. **Key Events Timeline**: Identify and organize the main events in chronological order, creating a clear
           timeline of what happened.

        4. **Main Participants**: Identify the key people, organizations, or entities involved in the incident/event
           and their roles.

        5. **Detailed Analysis**: Provide an in-depth analysis of the incident/event, including causes, processes,
           and immediate outcomes.

        6. **Multiple Perspectives**: Extract different viewpoints, opinions, and perspectives on the incident/event
           from various stakeholders or sources.

        7. **Impact and Consequences**: Analyze both immediate and long-term impacts, consequences, and changes
           that resulted from this incident/event.

        8. **Cultural and Social Context**: Identify relevant cultural, social, political, or economic factors
           that provide important context for understanding the incident.

        9. **Lessons Learned**: Extract key lessons, reforms, changes, or insights that emerged from this incident/event.

        10. **Verified Sources**: List the most credible and verified sources used in the research.

        11. **Factual Accuracy Notes**: Provide notes on the factual accuracy, reliability of sources, and any
            areas where information might be disputed or uncertain.

        Ensure the structured report is:
        - Factually accurate and well-sourced
        - Organized for clear narrative flow
        - Suitable for Hindi documentary-style narration
        - Culturally sensitive and contextually appropriate
        - Educational and informative for general audiences"""

        # Create the structuring task
        structuring_task = Task(
            description=task_description,
            agent=editor,
            expected_output=format_instructions
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[structuring_task],
            agents=[editor],
            verbose=self.verbose
        )

        try:
            crew_output = crew.kickoff()
            result = crew_output.raw

            # Parse the result using the Pydantic parser
            structured_report = StructuredReportParser.parse_output(parser, result)

            # If parsing fails, raise an error
            if structured_report is None:
                logger.error("Could not parse Editor result, Raw output: %s", result)
                raise ValueError("Failed to parse structured report from Editor Agent")

            logger.info("Research report structuring completed successfully")
            return structured_report

        except Exception as e:
            logger.error(f"Error structuring research report: {str(e)}")
            raise
