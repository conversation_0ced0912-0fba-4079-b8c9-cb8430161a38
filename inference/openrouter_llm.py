"""
OpenRouter Chat LLM
---------------
Custom LLM implementation for LangChain that integrates with OpenRouter API
to access various chat LLM models including open-source ones.
"""

import os
import logging
from typing import Optional

from langchain_community.chat_models import ChatOpenAI

logger = logging.getLogger(__name__)

class ChatOpenRouter(ChatOpenAI):
    openai_api_base: str
    openai_api_key: str
    model_name: str

    def __init__(self,
                 model_name: str,
                 api_key: Optional[str] = None,
                 api_base: str = "https://openrouter.ai/api/v1",
                 **kwargs):
        """
        Initialize the OpenRouter LLM.

        Args:
            model_name (str): The model to use.
            api_key (str, optional): The OpenRouter API key. If not provided, will use OPENROUTER_API_KEY env var.
            api_base (str): The OpenRouter API base URL. Defaults to "https://openrouter.ai/api/v1".
            **kwargs: Additional arguments to pass to the LLM.
        """
        api_key = api_key or os.getenv('OPENROUTER_API_KEY')
        super().__init__(openai_api_base=api_base,
                         openai_api_key=api_key,
                         model_name=model_name, **kwargs)
