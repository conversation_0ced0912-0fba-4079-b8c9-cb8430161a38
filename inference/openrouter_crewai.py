"""
OpenRouter CrewAI Adapter
---------------
Adapter that allows using OpenRouter's <PERSON><PERSON><PERSON><PERSON> integration with CrewAI.
"""

import os
import logging
from typing import Optional
from crewai import BaseLLM
from inference.openrouter_llm import ChatOpenRouter

logger = logging.getLogger(__name__)

class ChatOpenRouterCrewAI(BaseLLM):
    """
    A wrapper around LangChain's ChatOpenRouter for CrewAI that implements the CrewAI BaseLLM interface.
    """
    
    def __init__(
        self,
        model: str = "gpt-4o-mini",
        temperature: float = 0.7,
        api_key: Optional[str] = None,
        **kwargs
    ):
        """
        Initialize the OpenRouter LLM for CrewAI.
        
        Args:
            model (str): The model to use. Defaults to "gpt-4o-mini".
            temperature (float): The temperature for generation. Defaults to 0.7.
            api_key (str, optional): The OpenRouter API key. If not provided, will use OPENROUTER_API_KEY env var.
            **kwargs: Additional arguments to pass to the LLM.
        """
        super().__init__(model, temperature)
        
        self.model = model
        self.temperature = temperature
        api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        
        # Validate API key
        if not api_key:
            raise ValueError("OpenRouter API key is required. Set it via the api_key parameter or OPENROUTER_API_KEY environment variable.")
        
        # Initialize the ChatOpenRouter
        self.chat_model = ChatOpenRouter(
            model_name=model,
            api_key=api_key,
            temperature=temperature,
            **kwargs
        )
        
        logger.info(f"Initialized ChatOpenRouterCrewAI with model {model}")
    
    def call(self, messages, tools=None, callbacks=None, available_functions=None):
        """
        Call the LLM with the given messages.
        
        Args:
            messages: Input messages for the LLM
            tools: Optional list of tool schemas for function calling
            callbacks: Optional list of callback functions
            available_functions: Optional dict mapping function names to callables
            
        Returns:
            Either a text response from the LLM or the result of a tool function call
        """
        # Convert string message to proper format if needed
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        
        # Convert to LangChain message format
        from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
        
        lc_messages = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "system":
                lc_messages.append(SystemMessage(content=content))
            elif role == "user":
                lc_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                lc_messages.append(AIMessage(content=content))
        
        # Call the LangChain model
        response = self.chat_model.invoke(lc_messages)
        
        # Return the content of the response
        return response.content
    
    def supports_function_calling(self) -> bool:
        """Check if the LLM supports function calling."""
        return True
    
    def supports_stop_words(self) -> bool:
        """Check if the LLM supports stop words."""
        return True
    
    def get_context_window_size(self) -> int:
        """Get the context window size of the LLM."""
        # Default context window sizes for common models
        context_windows = {
            "gpt-4o": 128000,
            "gpt-4o-mini": 128000,
            "gpt-4": 8192,
            "gpt-4-32k": 32768,
            "gpt-3.5-turbo": 16385,
            "gpt-3.5-turbo-16k": 16385,
            "anthropic/claude-3-opus": 200000,
            "anthropic/claude-3-sonnet": 200000,
            "anthropic/claude-3-haiku": 200000,
            "mistralai/mixtral-8x7b": 32768,
            "mistralai/mistral-7b": 8192,
        }
        
        # Return the context window size for the model, or a default value
        return context_windows.get(self.model, 8192)
