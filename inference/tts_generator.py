"""
TTS Generator
------------
Provides text-to-speech generation for Hindi narration using multiple providers:
1. ElevenLabs TTS (API-based, production quality)
2. espeak-ng TTS (local processing, for development)

This module is designed for the shorts-based audio generation pipeline where:
- Audio files are generated for individual shorts segments
- Each short has its own audio/ subdirectory within the short's directory
- Audio files are stored as: shorts/short_N/audio/short_N_segment_M.mp3
- Supports --continue functionality by checking for existing audio files

The appropriate provider is selected based on the --dev flag.
"""

import os
import abc
import logging
import shutil
import subprocess
from typing import List
from elevenlabs.client import ElevenLabs

from models.schema import ShortSegment

logger = logging.getLogger(__name__)


class BaseTTSGenerator(abc.ABC):
    """
    Abstract base class for TTS generators.
    All TTS providers must implement this interface for shorts-based audio generation.
    """

    @abc.abstractmethod
    def generate_audio(self, segments: List[ShortSegment], output_dir: str) -> List[str]:
        """
        Generate audio files for each segment in the shorts-based pipeline.

        Args:
            segments (List[ShortSegment]): List of segments with narration.
                For shorts pipeline, these will be ShortSegment objects.
            output_dir (str): Directory to save audio files. This should be the audio/
                subdirectory within a specific short's directory (e.g., shorts/short_1/audio/).

        Returns:
            List[str]: List of audio file paths for the generated audio files.
        """
        pass


class ElevenLabsTTSGenerator(BaseTTSGenerator):
    """
    TTS generator that uses ElevenLabs API for high-quality Hindi narration.
    Optimized for shorts-based audio generation pipeline.

    Features:
    - Generates audio files for individual short segments
    - Supports --continue functionality by checking for existing files
    - Saves audio files in short-specific audio/ directories
    - Requires an API key and internet connection
    """

    def __init__(self, voice_id: str = "MaBqnF6LpI8cAT5sGihk"):
        """
        Initialize the ElevenLabs TTS generator with necessary API keys.

        Args:
            voice_id (str, optional): ElevenLabs voice ID to use.
        """
        self.elevenlabs_api_key = os.getenv("ELEVENLABS_API_KEY")

        # Use provided voice_id
        self.voice_id = voice_id

        if not self.elevenlabs_api_key:
            raise ValueError("Missing required API key for ElevenLabsTTSGenerator")

        # Initialize the ElevenLabs client
        self.client = ElevenLabs(api_key=self.elevenlabs_api_key)

        logger.info("Initialized ElevenLabs TTS Generator")

    def generate_audio(self, segments: List[ShortSegment], output_dir: str) -> List[str]:
        """
        Generate audio files for each segment using ElevenLabs TTS.

        This method is optimized for the shorts-based pipeline where each short's
        segments are processed individually and stored in the short's audio/ directory.

        Args:
            segments (List[ShortSegment]): List of segments with narration.
                In the shorts pipeline, these are ShortSegment objects.
            output_dir (str): Directory to save audio files. Should be the audio/ subdirectory
                within a specific short's directory (e.g., shorts/short_1/audio/).

        Returns:
            List[str]: List of audio file paths for successfully generated audio files.
        """
        logger.info(f"Starting audio generation with ElevenLabs for {len(segments)} segments")
        logger.info(f"Output directory: {output_dir}")

        audio_paths = []

        # Create the audio directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Process each segment
        for i, segment in enumerate(segments):
            primary_num = segment.short_number
            segment_num = segment.segment_number
            narration = segment.narration
            filename_prefix = "short"

            if not narration:
                logger.warning(f"Empty narration for segment {primary_num}-{segment_num}, skipping")
                continue

            # Generate a unique filename for this short's segment
            filename = os.path.join(output_dir, f"{filename_prefix}_{primary_num}_segment_{segment_num}.mp3")

            # Check if audio file already exists (--continue functionality)
            if os.path.exists(filename):
                logger.info(f"Audio file already exists for segment {primary_num}-{segment_num}, skipping generation")
                audio_paths.append(filename)
                continue

            logger.info(f"Generating audio for segment {i+1}/{len(segments)} (Short {primary_num}, Segment {segment_num})")

            try:
                # Generate audio using ElevenLabs
                audio_stream = self.client.text_to_speech.convert(
                    text=narration,
                    voice_id=self.voice_id,
                    model_id="eleven_multilingual_v2",
                    output_format="mp3_44100_128"
                )

                # Save the audio to a file - collect all chunks from the generator
                audio_data = b""
                for chunk in audio_stream:
                    if isinstance(chunk, bytes):
                        audio_data += chunk

                # Save the collected audio data to a file
                with open(filename, "wb") as f:
                    f.write(audio_data)

                # Add the file path to the list
                audio_paths.append(filename)

                logger.info(f"Audio saved to {filename}")
            except Exception as e:
                logger.error(f"Error generating audio for segment {primary_num}-{segment_num}: {str(e)}")
                # Continue with the next segment

        logger.info(f"ElevenLabs audio generation completed successfully")
        logger.info(f"Generated {len(audio_paths)} audio files in {output_dir}")
        return audio_paths


class EspeakTTSGenerator(BaseTTSGenerator):
    """
    TTS generator that uses espeak-ng for local Hindi narration processing.
    Optimized for shorts-based audio generation pipeline.

    Features:
    - Generates audio files for individual short segments
    - Supports --continue functionality by checking for existing files
    - Saves audio files in short-specific audio/ directories
    - Does not require an API key or internet connection, suitable for development
    - Requires espeak-ng to be installed on the system
    """

    def __init__(self, voice: str = "hi", speed: int = 150, pitch: int = 50):
        """
        Initialize the espeak-ng TTS generator with Hindi language settings.

        Args:
            voice (str, optional): espeak-ng voice to use. Default is 'hi' for Hindi.
            speed (int, optional): Speech speed in words per minute. Default is 150.
            pitch (int, optional): Speech pitch (0-99). Default is 50.
        """
        self.voice = voice
        self.speed = speed
        self.pitch = pitch

        # Check if espeak-ng is installed
        if not self._check_espeak_installation():
            raise ValueError(
                "espeak-ng is not installed or not found in PATH. "
                "Please install espeak-ng using: brew install espeak"
            )

        # Check if ffmpeg is available for audio conversion
        if not shutil.which("ffmpeg"):
            logger.warning(
                "ffmpeg not found. Audio files will be saved as WAV instead of MP3. "
                "Install ffmpeg for MP3 support: brew install ffmpeg"
            )
            self.use_mp3 = False
        else:
            self.use_mp3 = True

        logger.info(f"Initialized espeak-ng TTS Generator with voice: {voice}, speed: {speed}, pitch: {pitch}")

    def _check_espeak_installation(self) -> bool:
        """
        Check if espeak-ng is installed and accessible.

        Returns:
            bool: True if espeak-ng is available, False otherwise.
        """
        try:
            result = subprocess.run(
                ["espeak", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False

    def generate_audio(self, segments: List[ShortSegment], output_dir: str) -> List[str]:
        """
        Generate audio files for each segment using espeak-ng TTS.

        This method is optimized for the shorts-based pipeline where each short's
        segments are processed individually and stored in the short's audio/ directory.

        Args:
            segments (List[ShortSegment]): List of segments with narration.
                In the shorts pipeline, these are ShortSegment objects.
            output_dir (str): Directory to save audio files. Should be the audio/ subdirectory
                within a specific short's directory (e.g., shorts/short_1/audio/).

        Returns:
            List[str]: List of audio file paths for successfully generated audio files.
        """
        logger.info(f"Starting audio generation with espeak-ng TTS for {len(segments)} segments")
        logger.info(f"Output directory: {output_dir}")

        audio_paths = []

        # Create the audio directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Process each segment
        for i, segment in enumerate(segments):
            primary_num = segment.short_number
            segment_num = segment.segment_number
            narration = segment.narration
            filename_prefix = "short"

            if not narration:
                logger.warning(f"Empty narration for segment {primary_num}-{segment_num}, skipping")
                continue

            # Generate filenames for this short's segment
            if self.use_mp3:
                final_filename = os.path.join(output_dir, f"{filename_prefix}_{primary_num}_segment_{segment_num}.mp3")
                temp_wav_filename = os.path.join(output_dir, f"{filename_prefix}_{primary_num}_segment_{segment_num}_temp.wav")
            else:
                final_filename = os.path.join(output_dir, f"{filename_prefix}_{primary_num}_segment_{segment_num}.wav")
                temp_wav_filename = final_filename

            # Check if audio file already exists (--continue functionality)
            if os.path.exists(final_filename):
                logger.info(f"Audio file already exists for segment {primary_num}-{segment_num}, skipping generation")
                audio_paths.append(final_filename)
                continue

            logger.info(f"Generating audio for segment {i+1}/{len(segments)} (Short {primary_num}, Segment {segment_num})")

            try:
                # Generate audio using espeak-ng
                espeak_cmd = [
                    "espeak",
                    "-v", self.voice,  # Voice (Hindi)
                    "-s", str(self.speed),  # Speed in words per minute
                    "-p", str(self.pitch),  # Pitch (0-99)
                    "-w", temp_wav_filename,  # Output to WAV file
                    narration  # Text to speak
                ]

                result = subprocess.run(
                    espeak_cmd,
                    capture_output=True,
                    text=True,
                    timeout=30  # 30 second timeout per segment
                )

                if result.returncode != 0:
                    logger.error(f"espeak-ng failed for segment {primary_num}-{segment_num}: {result.stderr}")
                    continue

                # Convert WAV to MP3 if ffmpeg is available
                if self.use_mp3 and temp_wav_filename != final_filename:
                    ffmpeg_cmd = [
                        "ffmpeg",
                        "-i", temp_wav_filename,
                        "-acodec", "mp3",
                        "-ab", "128k",  # 128 kbps bitrate
                        "-y",  # Overwrite output file
                        final_filename
                    ]

                    ffmpeg_result = subprocess.run(
                        ffmpeg_cmd,
                        capture_output=True,
                        text=True,
                        timeout=30
                    )

                    if ffmpeg_result.returncode == 0:
                        # Remove temporary WAV file
                        os.remove(temp_wav_filename)
                    else:
                        logger.warning(f"FFmpeg conversion failed for segment {primary_num}-{segment_num}, keeping WAV file")
                        final_filename = temp_wav_filename

                # Add the file path to the list
                audio_paths.append(final_filename)

                logger.info(f"Audio saved to {final_filename}")

            except subprocess.TimeoutExpired:
                logger.error(f"Timeout generating audio for segment {primary_num}-{segment_num}")
                # Clean up temporary file if it exists
                if os.path.exists(temp_wav_filename):
                    os.remove(temp_wav_filename)
            except Exception as e:
                logger.error(f"Error generating audio for segment {primary_num}-{segment_num}: {str(e)}")
                # Clean up temporary file if it exists
                if os.path.exists(temp_wav_filename):
                    os.remove(temp_wav_filename)

        logger.info(f"espeak-ng audio generation completed successfully")
        logger.info(f"Generated {len(audio_paths)} audio files in {output_dir}")
        return audio_paths


def create_tts_generator(dev_mode: bool = True, voice_id: str = None) -> BaseTTSGenerator:
    """
    Factory function to create the appropriate TTS generator for the shorts-based pipeline.

    This function creates TTS generators optimized for the shorts-based audio generation
    pipeline where audio files are generated for individual short segments and stored
    in short-specific audio/ directories.

    Args:
        dev_mode (bool): Whether to use the development (espeak-ng) TTS generator.
            If True, uses espeak-ng TTS for local processing (no API costs).
            If False, uses ElevenLabs TTS for production quality.
        voice_id (str, optional): ElevenLabs voice ID to use when dev_mode is False.
            If not provided, will use the ELEVENLABS_VOICE_ID environment variable
            or fall back to the default voice ID.

    Returns:
        BaseTTSGenerator: The appropriate TTS generator instance configured for shorts pipeline.
    """
    if dev_mode:
        logger.info("Creating espeak-ng TTS Generator for shorts pipeline (dev mode)")
        return EspeakTTSGenerator()

    else:
        # Use the provided voice_id, or get it from environment, or use the default
        if not voice_id:
            voice_id = os.getenv("ELEVENLABS_VOICE_ID", "MaBqnF6LpI8cAT5sGihk")

        logger.info(f"Creating ElevenLabs TTS Generator for shorts pipeline (production mode) with voice ID: {voice_id}")
        return ElevenLabsTTSGenerator(voice_id=voice_id)
