"""
Replicate Chat LLM
------------------
Custom LLM implementation for LangChain that integrates with Replicate API
to access various chat LLM models including open-source ones like Llama, Mistral, etc.
"""

import os
import logging
from typing import Optional, List, Dict, Any

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage, AIMessage
from langchain_core.outputs import ChatResult, ChatGeneration
from langchain_core.callbacks import CallbackManagerForLLMRun
import replicate

logger = logging.getLogger(__name__)


class ChatReplicate(BaseChatModel):
    """
    LangChain-compatible chat model that uses Replicate API.
    
    This class provides a LangChain interface for Replicate's chat models,
    allowing seamless integration with existing LangChain workflows.
    """
    
    model_name: str
    api_token: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 1000
    top_p: float = 1.0
    
    def __init__(self,
                 model_name: str = "meta/llama-2-70b-chat",
                 api_token: Optional[str] = None,
                 temperature: float = 0.7,
                 max_tokens: int = 1000,
                 top_p: float = 1.0,
                 **kwargs):
        """
        Initialize the Replicate LLM.

        Args:
            model_name (str): The Replicate model to use. Default: "meta/llama-2-70b-chat"
            api_token (str, optional): The Replicate API token. If not provided, will use REPLICATE_API_KEY env var.
            temperature (float): The temperature for generation. Default: 0.7
            max_tokens (int): Maximum tokens to generate. Default: 1000
            top_p (float): Top-p sampling parameter. Default: 1.0
            **kwargs: Additional arguments to pass to the parent class.
        """
        super().__init__(**kwargs)
        
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        
        # Set up API token
        self.api_token = api_token or os.getenv('REPLICATE_API_KEY')
        if not self.api_token:
            raise ValueError("Replicate API token is required. Set it via the api_token parameter or REPLICATE_API_KEY environment variable.")
        
        # Configure replicate client
        os.environ['REPLICATE_API_TOKEN'] = self.api_token
        
        logger.info(f"Initialized ChatReplicate with model {model_name}")
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """
        Generate a response using the Replicate API.
        
        Args:
            messages: List of messages to send to the model
            stop: Optional list of stop sequences
            run_manager: Optional callback manager
            **kwargs: Additional arguments
            
        Returns:
            ChatResult: The generated response
        """
        # Convert messages to the format expected by Replicate
        prompt = self._messages_to_prompt(messages)
        
        # Prepare input parameters
        input_params = {
            "prompt": prompt,
            "temperature": kwargs.get("temperature", self.temperature),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            "top_p": kwargs.get("top_p", self.top_p),
        }
        
        # Add stop sequences if provided
        if stop:
            input_params["stop_sequences"] = stop
        
        try:
            # Call Replicate API
            logger.debug(f"Calling Replicate model {self.model_name} with prompt length: {len(prompt)}")
            
            output = replicate.run(self.model_name, input=input_params)
            
            # Handle different output formats
            if isinstance(output, list):
                response_text = "".join(output)
            elif isinstance(output, str):
                response_text = output
            else:
                response_text = str(output)
            
            # Create the response
            message = AIMessage(content=response_text)
            generation = ChatGeneration(message=message)
            
            return ChatResult(generations=[generation])
            
        except Exception as e:
            logger.error(f"Error calling Replicate API: {str(e)}")
            raise e
    
    def _messages_to_prompt(self, messages: List[BaseMessage]) -> str:
        """
        Convert LangChain messages to a prompt string for Replicate.
        
        Args:
            messages: List of LangChain messages
            
        Returns:
            str: Formatted prompt string
        """
        prompt_parts = []
        
        for message in messages:
            if isinstance(message, SystemMessage):
                prompt_parts.append(f"System: {message.content}")
            elif isinstance(message, HumanMessage):
                prompt_parts.append(f"Human: {message.content}")
            elif isinstance(message, AIMessage):
                prompt_parts.append(f"Assistant: {message.content}")
            else:
                prompt_parts.append(f"User: {message.content}")
        
        # Add assistant prompt at the end
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)
    
    @property
    def _llm_type(self) -> str:
        """Return identifier for the LLM type."""
        return "replicate"
    
    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """Get the identifying parameters."""
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
        }
