# AI Hindi Documentary Audio Generator

An autonomous Python-based agent that creates Hindi documentary-style audio content based on real incidents using in-depth research. Generates complete story data, video metadata, and high-quality narration audio files ready for manual video assembly.

## 🔧 Features

- Fully autonomous documentary generation pipeline
- In-depth research using Perplexity AI Sonar for factual accuracy
- Hindi documentary-style narration inspired by popular creators like <PERSON><PERSON><PERSON> Rajput

- Video metadata generation with trending keywords from Google Trends
- High-quality text-to-speech narration
- Dialogue segmentation for optimal audio pacing
- Audio-only output for manual video assembly

## 🌐 Language Configuration

The system is designed with the following language configuration:

- **Narration Text**: All story narration is generated in modern conversational Hindi that naturally incorporates commonly-used English terms. This creates natural, relatable content for modern Hindi speakers who commonly mix English words in everyday conversation.
- **Visual Descriptions**: All visual descriptions for image generation are in English.
- **Video Metadata**: All video metadata (keywords, titles, descriptions, hashtags) are generated in English for optimal SEO and discoverability.
- **Technical Elements**: All other elements of the system (scene transitions, narrative purpose, file names, logs, etc.) are in English.

This configuration ensures that the final video has natural, modern Hindi narration while maintaining optimal discoverability and technical functionality.

## 🔄 Pipeline Overview

The new simplified pipeline focuses exclusively on real stories with factual accuracy:

1. **Query Generation**: AI agent generates detailed research queries based on title and context
2. **In-depth Research**: Perplexity AI Sonar conducts comprehensive research using the generated query
3. **Report Structuring**: AI agent structures the research report into documentary-ready format
4. **Documentary Narration**: AI agent creates Hindi documentary-style narration from structured report
5. **Interactive Story Editing**: Mandatory review and editing of the generated story
6. **Audio Segmentation**: Story is split into optimal segments for narration
7. **Audio Generation**: High-quality Hindi narration audio is generated
8. **Video Metadata Generation**: AI agent creates optimized titles, descriptions, and hashtags using trending keywords

## 📑 Project Structure

```
ai-video-story-generator/
├── main.py                 # Main orchestrator script
├── agents/                 # AI agents for different tasks
│   ├── query_generator.py  # Generates research queries for Perplexity AI
│   ├── editor.py           # Structures Perplexity research reports
│   ├── writer.py           # Creates Hindi documentary narration
│   ├── video_metadata.py   # Generates video titles, descriptions, hashtags
│   ├── dialogue_splitter.py # Splits story into segments
│   └── story_editor.py     # Interactive story editing
├── models/                 # Pydantic data models
│   ├── __init__.py         # Package initialization
│   └── schema.py           # Data schema definitions
├── inference/              # Third-party API clients
│   ├── perplexity_client.py # Perplexity AI Sonar integration
│   ├── dataforseo_client.py # DataForSEO Google Trends integration
│   ├── tts_generator.py    # TTS integration (ElevenLabs and espeak-ng)
│   ├── openrouter_llm.py   # OpenRouter LLM integration
│   ├── openrouter_crewai.py # OpenRouter CrewAI adapter
│   ├── replicate_llm.py    # Replicate LLM integration
│   └── replicate_crewai.py # Replicate CrewAI adapter
├── utils/                  # Utility modules
│   ├── parsers.py          # Langchain output parsers
│   ├── rate_limited_llm.py # Rate limiting for LLM calls
│   ├── agent_factory.py    # Agent creation utilities
└── assets/                 # Generated assets (per story directory)
    ├── query.md            # Generated research query for Perplexity AI
    ├── report.json         # Raw Perplexity AI research report
    ├── structured_report.json # Structured research report
    ├── story.json          # Hindi documentary story data
    ├── video_metadata.json # Video titles, descriptions, hashtags
    ├── dialogue_segments.json # Segmented dialogue data
    └── audio/              # Generated audio files
```

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- API keys for:
  - **Perplexity AI** (required for in-depth research reports)
  - **DataForSEO** (required for Google Trends keyword analysis)
  - OpenAI (for LLM models like GPT-4o-mini) - Optional if using other providers
  - OpenRouter (for accessing multiple LLM models) - Optional if using other providers
  - Replicate (for open-source LLMs and image generation) - Optional if using other providers
  - ElevenLabs (TTS) - Optional when using espeak-ng TTS in dev mode
  - DeepInfra (FLUX-1-dev) - Optional if using other image providers
  - Black Forest Labs (BFL) - Optional if using other image providers
- espeak-ng package (installed via system package manager) for local development mode

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/ai-video-story-generator.git
   cd ai-video-story-generator
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file with your API keys:
   ```
   # Required for research and trending analysis
   PERPLEXITY_API_KEY=your_perplexity_api_key
   DATAFORSEO_API_KEY=your_dataforseo_api_key

   # LLM Provider API Keys (choose one or more)
   OPENAI_API_KEY=your_openai_api_key
   OPENROUTER_API_KEY=your_openrouter_api_key
   REPLICATE_API_KEY=your_replicate_api_key

   # TTS API Key (optional if using --dev mode)
   ELEVENLABS_API_KEY=your_elevenlabs_api_key

   # Image Generation API Keys (choose one)
   DEEPINFRA_API_KEY=your_deepinfra_api_key
   BFL_API_KEY=your_bfl_api_key
   # Note: REPLICATE_API_KEY can also be used for image generation
   ```

   Note: Other configuration options that were previously in the `.env` file have been moved to command-line arguments.

   You'll need to sign up for the following services to get the API keys:
   - [Perplexity AI](https://www.perplexity.ai/) - For in-depth research reports (required)
   - [DataForSEO](https://dataforseo.com/) - For Google Trends keyword analysis (required)
   - [OpenAI](https://platform.openai.com/signup) - For LLM models (GPT-4o-mini, etc.)
   - [OpenRouter](https://openrouter.ai/) - For accessing multiple LLM models (Claude, Llama, etc.)
   - [Replicate](https://replicate.com/) - For open-source models and image generation
   - [ElevenLabs](https://elevenlabs.io/sign-up) - For text-to-speech
   - [DeepInfra](https://deepinfra.com/signup) - For FLUX-1-dev image generation
   - [Black Forest Labs](https://api.bfl.ai/) - For direct FLUX API access

### Usage

Run the script with a story title for real incident-based documentaries:

```bash
# Basic usage - real incident documentary
python main.py --title "मुंबई की बारिश" --context "2023 में मुंबई में आई भीषण बाढ़"

# Without additional context (AI will generate research query from title alone)
python main.py --title "चंद्रयान-3 मिशन"
```

The script will:
1. Generate a detailed research query based on the title and context (saved to query.md)
2. Conduct in-depth research using Perplexity AI Sonar
3. Structure the research report into documentary-ready format
4. Create modern conversational Hindi documentary-style narration with natural English term integration
5. Allow interactive editing of the generated story (mandatory step)
6. Split dialogue into optimal segments for narration
7. Generate English video metadata with trending keywords optimized for SEO
8. Create high-quality narration audio files
9. Save all data in organized JSON format for manual video assembly

## 🔖 Configuration

You can customize the behavior by passing command-line arguments:

### Required Arguments

- `--title`: Title of the story/incident (required)

### Optional Arguments

- `--context`: Additional context for the story (optional, helps generate better research queries)

### Image Generation Configuration



### Audio Generation Configuration

- `--elevenlabs-voice-id`: ElevenLabs voice ID to use for narration. Default: `MaBqnF6LpI8cAT5sGihk`
- `--dev`: Use espeak-ng TTS for local development (no API costs). Default: `True`
- `--no-dev`: Use ElevenLabs TTS for production quality (requires API key). Default: `False`

### LLM Model Configuration

- `--model`: LLM model to use for all agents. Default: `gpt-4o-mini`
- `--provider`: LLM provider to use (`openai`, `openrouter`, or `replicate`). Default: `openai`
- `--max-tokens-per-minute`: Maximum tokens per minute for OpenAI API rate limiting. Default: `30000`

### Agent-Specific LLM Configuration

You can configure different models and providers for individual agents. If not specified, agents will use the global `--model` and `--provider` settings.

#### QueryGenerationAgent Configuration
- `--query-generator-model`: LLM model for QueryGenerationAgent (defaults to `--model`)
- `--query-generator-provider`: LLM provider for QueryGenerationAgent (defaults to `--provider`)

#### EditorAgent Configuration
- `--editor-model`: LLM model for EditorAgent (defaults to `--model`)
- `--editor-provider`: LLM provider for EditorAgent (defaults to `--provider`)

#### WriterAgent Configuration
- `--writer-model`: LLM model for WriterAgent (defaults to `--model`)
- `--writer-provider`: LLM provider for WriterAgent (defaults to `--provider`)

#### VideoMetadataAgent Configuration
- `--video-metadata-model`: LLM model for VideoMetadataAgent (defaults to `--model`)
- `--video-metadata-provider`: LLM provider for VideoMetadataAgent (defaults to `--provider`)

#### DialogueSplitterAgent Configuration
- `--dialogue-splitter-model`: LLM model for DialogueSplitterAgent (defaults to `--model`)
- `--dialogue-splitter-provider`: LLM provider for DialogueSplitterAgent (defaults to `--provider`)

#### ImageDescriptionGenerator Configuration
- `--image-desc-gen-model`: LLM model for ImageDescriptionGenerator (defaults to `--model`)
- `--image-desc-gen-provider`: LLM provider for ImageDescriptionGenerator (defaults to `--provider`)

#### StoryEditorAgent Configuration
- `--story-editor-model`: LLM model for StoryEditorAgent (defaults to `--model`)
- `--story-editor-provider`: LLM provider for StoryEditorAgent (defaults to `--provider`)

### Provider-Specific Information

#### OpenAI Provider
- Supports all GPT models (GPT-4, GPT-3.5, etc.)
- Requires `OPENAI_API_KEY` in `.env` file
- Includes built-in rate limiting

#### OpenRouter Provider
- Access to multiple models: Claude, Llama, Mistral, etc.
- Requires `OPENROUTER_API_KEY` in `.env` file
- Cost-effective alternative to OpenAI

#### Replicate Provider
- Access to open-source models: Llama, Mistral, CodeLlama, etc.
- Requires `REPLICATE_API_KEY` in `.env` file
- Good for experimenting with different model architectures

### Research Configuration

- `--reasoning-effort`: Reasoning effort level for Perplexity AI research. Choices: `low`, `medium`, `high`. Default: `low`

### Debug and Logging Configuration

- `--verbose`: Enable verbose output from CrewAI agents. Default: `False`
- `--interactive-editing`: Enable interactive story editing workflow. Default: `True`

### Video Output Configuration

- `--create-fast-version`: Create an additional 2x speed video output for quick review. Default: `False`

### Example with Advanced Options

```bash
python main.py --title "भोपाल गैस त्रासदी" \
  --context "1984 में भोपाल में हुई औद्योगिक दुर्घटना" \
  --reasoning-effort high \
  --image-style realistic \
  --deepinfra-model "black-forest-labs/FLUX-1.1-pro" \
  --no-dev \
  --elevenlabs-voice-id "MaBqnF6LpI8cAT5sGihk" \
  --model "gpt-4o" \
  --provider "openai" \
  --max-tokens-per-minute 30000 \
  --max-concurrent-requests 100 \
  --verbose \
  --create-fast-version
```

### Example with Agent-Specific Configuration

```bash
python main.py --title "कारगिल युद्ध" \
  --context "1999 में भारत-पाकिस्तान के बीच हुआ युद्ध" \
  --model "gpt-4o-mini" \
  --provider "openai" \
  --query-generator-model "gpt-4o" \
  --query-generator-provider "openrouter" \
  --writer-model "claude-3-sonnet" \
  --writer-provider "openrouter" \
  --video-metadata-model "gpt-4o" \
  --image-desc-gen-model "gpt-4o" \
  --dialogue-splitter-provider "openrouter" \
  --verbose
```

This example shows:
- Global defaults: `gpt-4o-mini` model with `openai` provider
- QueryGenerationAgent: Uses `gpt-4o` model with `openrouter` provider
- WriterAgent: Uses `claude-3-sonnet` model with `openrouter` provider
- VideoMetadataAgent: Uses `gpt-4o` model with default `openai` provider
- ImageDescriptionGenerator: Uses `gpt-4o` model with default `openai` provider
- DialogueSplitterAgent: Uses default `gpt-4o-mini` model with `openrouter` provider
- EditorAgent and StoryEditorAgent: Use global defaults

### Example with Replicate Provider

```bash
python main.py --title "मंगल मिशन" \
  --context "भारत का मंगलयान मिशन और उसकी सफलता" \
  --provider "replicate" \
  --model "meta/llama-2-70b-chat" \
  --image-provider "replicate" \
  --image-model "black-forest-labs/flux-schnell" \
  --writer-model "mistralai/mixtral-8x7b-instruct-v0.1" \
  --verbose
```

This example shows:
- Using Replicate for both text and image generation
- Global LLM: Llama 2 70B Chat via Replicate
- Image generation: FLUX Schnell via Replicate
- WriterAgent: Mixtral 8x7B via Replicate
- Other agents: Use global Llama 2 70B Chat

For local development without API costs:

```bash
python main.py --title "2008 मुंबई हमले" \
  --context "26/11 के आतंकी हमले की घटना" \
  --reasoning-effort medium \
  --image-style realistic \
  --dev \
  --model "gpt-4o-mini" \
  --interactive-editing
```

API keys should still be configured in the `.env` file:

## 🧩 Data Models and Parsers

The project uses Pydantic models and Langchain parsers to ensure consistent data formatting throughout the pipeline:

### Pydantic Models

- `PerplexityReport`: Raw research report data from Perplexity AI Sonar
- `StructuredReport`: Organized research report for documentary narration
- `VideoMetadata`: Video titles, descriptions, and hashtags with trending keywords
- `Story`: Complete documentary story with title and scenes
- `Scene`: Individual scene with Hindi narration
- `SceneSegment`: Segment of a scene with narration (5-8 seconds duration)
- `ImagePrompt`: Image generation prompt for a scene segment
- `TrendingKeyword`: Keyword trend data from DataForSEO Google Trends

### Langchain Parsers

The project uses Langchain's `PydanticOutputParser` to ensure that LLM outputs conform to the expected data structures. This helps prevent formatting issues and ensures all scenes are properly processed.

## 📝 Notes

- The first run may take some time as it generates all assets from scratch
- API usage costs apply for Perplexity AI, DataForSEO, OpenAI, and ElevenLabs services
- The system focuses exclusively on real incidents for factual accuracy
- Interactive story editing is mandatory to ensure quality and accuracy
- Video metadata generation helps optimize content for social media platforms
- Structured data models ensure consistent processing of all scenes
- All narration is generated in Hindi for authentic documentary experience

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgements

- [CrewAI](https://github.com/joaomdmoura/crewai) for agent orchestration
- [Perplexity AI](https://www.perplexity.ai/) for in-depth research capabilities
- [DataForSEO](https://dataforseo.com/) for Google Trends keyword analysis
- [ElevenLabs](https://elevenlabs.io/) for production-quality text-to-speech
- [espeak-ng](https://github.com/espeak-ng/espeak-ng) for local development text-to-speech
- [FLUX-1-dev](https://deepinfra.com/) for image generation
- Nitish Rajput and other Hindi documentary creators for narration style inspiration
