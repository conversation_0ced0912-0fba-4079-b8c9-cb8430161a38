<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.7">
  <diagram name="Page-1" id="c4JYOAFvPPofIv-mRXkl">
    <mxGraphModel dx="1803" dy="670" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="h31jOwt_lBUDQCrtWlnl-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-1" value="User Input&lt;br&gt;- Title&lt;br&gt;- Context" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="310" y="30" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-3" value="Agents" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="310" y="140" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="570" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-8" value="QueryGenerator&lt;div&gt;&lt;br&gt;&lt;div&gt;Generates a detailed query from provided title and context&amp;nbsp;&lt;/div&gt;&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="270" y="260" width="200" height="150" as="geometry" />
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="650" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="h31jOwt_lBUDQCrtWlnl-10" target="souuxHOGcpTYGXGULKiS-33" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="560" y="650" as="targetPoint" />
            <Array as="points">
              <mxPoint x="561" y="530" />
              <mxPoint x="561" y="580" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-10" value="Researcher&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Generates a research report using the query generated by QueryGenerator&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="255" y="450" width="230" height="160" as="geometry" />
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="820" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-12" value="Editor&lt;br&gt;&lt;br&gt;Structured the raw report generated by Researcher" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="280" y="650" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-14" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="980" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-14" value="Writer&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Writes a engaging story from structured report&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="275" y="820" width="190" height="120" as="geometry" />
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="367.5" y="1150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-16" value="Editing&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Used to manually edit the story generated by Writer&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="265" y="980" width="205" height="130" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="h31jOwt_lBUDQCrtWlnl-18" target="souuxHOGcpTYGXGULKiS-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370.0050000000001" y="1320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="90" y="1330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h31jOwt_lBUDQCrtWlnl-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="650" y="1320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h31jOwt_lBUDQCrtWlnl-18" value="ShortsGenerator&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;This is the new Agent which will use to generate the shorts&lt;/div&gt;&lt;div&gt;&amp;nbsp;from the final story.json&lt;span style=&quot;white-space: pre;&quot;&gt;&#x9;&lt;/span&gt;&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="263.13" y="1150" width="213.75" height="140" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="souuxHOGcpTYGXGULKiS-4" target="souuxHOGcpTYGXGULKiS-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-4" value="Each short should be extracted show well that it should be like a stand alone story but related to the previous or next part" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="237.5" y="1480" width="260" height="100" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-5" value="Generates shorts from the story" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="30" y="1330" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="souuxHOGcpTYGXGULKiS-6" target="souuxHOGcpTYGXGULKiS-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-6" value="Each short length in audio should be between 20 seconds to 2 minutes the ideal length is 20 seconds to 1 minute only exceed the 1 minute if the part story narration is required to be in the same short if not added it will be unfinished or not proper" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="414" y="1320" width="550" height="140" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="souuxHOGcpTYGXGULKiS-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="366.5" y="1940" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-7" value="Goal&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;I have decided to upload the short videos of the story in parts on youtube instead of a one whole video&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="200.75" y="1700" width="333.5" height="180" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.6;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="souuxHOGcpTYGXGULKiS-5" target="souuxHOGcpTYGXGULKiS-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="souuxHOGcpTYGXGULKiS-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="367.5" y="2090" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-12" value="Store Each short inside the story generated directory" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="296.25" y="1940" width="133.75" height="100" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="souuxHOGcpTYGXGULKiS-14" target="souuxHOGcpTYGXGULKiS-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-14" value="Each short will be stored in a separate directory like short-1, short-2, short-3, short-x" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="285.01" y="2090" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="souuxHOGcpTYGXGULKiS-15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370.01" y="2460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-15" value="ShortsEditing&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;For each short one by one a shorts editor agent there to edit the short script it will have all the options which story editor has.&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="229.01" y="2240" width="282" height="170" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="souuxHOGcpTYGXGULKiS-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="369.9949999999999" y="2870" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-18" value="ShortsSplitter&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;We will have new a ShortsSplitter agent instead of dialogue splitter. Shorts splitter will analyse a short narration and split it into 5, 6 or 10 seconds narration. The split narration expected audio length should be 5, 6 or 10 seconds because later for creating visuals we will use text to video llms which is capable of generating video in the same length (5, 6 or 10 seconds)&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Will pass the each short narration to the agent one by one and the generated splitted narration will be stored in the respective short directory&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="82.81" y="2460" width="574.37" height="360" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="souuxHOGcpTYGXGULKiS-20" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="3100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-20" value="Generate Audio&lt;div&gt;For each short generate the audio and store the related short directory&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="245.01" y="2870" width="250" height="160" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-26" value="Video Metadata&lt;div&gt;Now in the Video Metadata we will generate a single title, base hashtags which will fit for all the shorts videos i will add the part number while uploading it to youtube like `Part 1 | video-title`, `Part 2 | video-title` etc. And with the base hashtags we will generate addition hashtags for each shorts which should be only for that particular short. So there will be sub agents in the video metadata the first one task is to generate keywords(this is we are already doing) which then be used to generate title and description. Once we have the keywords we will generate a single title and base hashtag for all shorts we will input the entire story to generate this. And second task is to generate the hashtags and description(using the generated keywords) specifically for a particular short, each short should be process one by one. The base title and hashtag data will be stored in the parent story directory and description and additional hashtags will be stored in the short directory.&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="3950" width="556.55" height="590" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="souuxHOGcpTYGXGULKiS-30" target="h31jOwt_lBUDQCrtWlnl-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-30" value="QueryEditingAgent" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="511" y="410" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="souuxHOGcpTYGXGULKiS-33" target="h31jOwt_lBUDQCrtWlnl-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="souuxHOGcpTYGXGULKiS-33" value="Research report review" style="ellipse;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="590" y="580" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="qnMBIaVEEDtIvi6p-QHr-1" value="VisualGenerator&lt;div&gt;After the Audio is generated we want to add another step to generate the visuals. For this we will use replicate `bytedance/seedance-1-lite` model which can generate video at 480p or 720p, 5 or 10 seconds duration. The video resolution will be provided as command line `--video-resolution` default will be 480p. We will pass one by one each short segments script and the agent task is to generate a engaging visual prompt for each segment narration which should be well suited for the model we are using. The generated visual should be engaging and properly sync with the segment narration(this is the most important). The generated visual file will be stored in the short directory. Then using the generated visual script for each narration we will generated the visuals using the model as mentioned above. All the generated visuals will be stored in the new directory `video` within the short directory the file name will be the same format as we are using in the audio directory. The both audio and video directory files should be related to each other like for example in audio directory we have a file name `short_1_segment_1.mp3` the corresponding visual file name of that audio should be `short_1_segment_1.mp4` in video directory within the that particular short directory. Its also should be functional for the `--continue` command so it should start exactly it left off. Also we will sequentially generate the visual on replicate no need to make multiple calls at the same time.&lt;/div&gt;" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-80" y="3100" width="907.49" height="510" as="geometry" />
        </mxCell>
        <mxCell id="qnMBIaVEEDtIvi6p-QHr-3" value="VideoAudioEditor" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="285.01" y="3750" width="257" height="220" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
