"""
Shorts Utilities
---------------
Utility functions for managing shorts directories and files.
"""

import os
import json
import logging
from typing import List, Dict, Optional, Tuple

from models.schema import Short, ShortSegment

logger = logging.getLogger(__name__)


def create_shorts_directories(story_dir: str, num_shorts: int) -> List[str]:
    """
    Create directories for shorts within the story directory.

    Args:
        story_dir (str): Path to the main story directory
        num_shorts (int): Number of shorts to create directories for

    Returns:
        List[str]: List of created short directory paths
    """
    short_dirs = []
    
    for i in range(1, num_shorts + 1):
        short_dir = os.path.join(story_dir, f"short-{i}")
        os.makedirs(short_dir, exist_ok=True)
        
        # Create subdirectories for audio and other assets
        os.makedirs(os.path.join(short_dir, 'audio'), exist_ok=True)
        
        short_dirs.append(short_dir)
        logger.info(f"Created directory: {short_dir}")
    
    return short_dirs


def get_shorts_directories(story_dir: str) -> List[str]:
    """
    Get all existing shorts directories in the story directory.

    Args:
        story_dir (str): Path to the main story directory

    Returns:
        List[str]: List of existing short directory paths, sorted by number
    """
    short_dirs = []
    
    if not os.path.exists(story_dir):
        return short_dirs
    
    for item in os.listdir(story_dir):
        item_path = os.path.join(story_dir, item)
        if os.path.isdir(item_path) and item.startswith("short-"):
            short_dirs.append(item_path)
    
    # Sort by short number
    short_dirs.sort(key=lambda x: int(x.split("short-")[-1]))
    
    return short_dirs


def load_short_from_directory(short_dir: str) -> Optional[Short]:
    """
    Load a short from its directory.

    Args:
        short_dir (str): Path to the short directory

    Returns:
        Optional[Short]: The loaded short, or None if not found/invalid
    """
    short_json_path = os.path.join(short_dir, "short.json")
    
    if not os.path.exists(short_json_path):
        logger.warning(f"Short JSON not found: {short_json_path}")
        return None
    
    try:
        with open(short_json_path, 'r', encoding='utf-8') as f:
            short_data = json.load(f)
            return Short(**short_data)
    except Exception as e:
        logger.error(f"Error loading short from {short_json_path}: {str(e)}")
        return None


def load_segments_from_directory(short_dir: str) -> Optional[List[ShortSegment]]:
    """
    Load segments from a short directory.

    Args:
        short_dir (str): Path to the short directory

    Returns:
        Optional[List[ShortSegment]]: List of segments, or None if not found/invalid
    """
    segments_json_path = os.path.join(short_dir, "segments.json")
    
    if not os.path.exists(segments_json_path):
        logger.warning(f"Segments JSON not found: {segments_json_path}")
        return None
    
    try:
        with open(segments_json_path, 'r', encoding='utf-8') as f:
            segments_data = json.load(f)
            return [ShortSegment(**segment_data) for segment_data in segments_data]
    except Exception as e:
        logger.error(f"Error loading segments from {segments_json_path}: {str(e)}")
        return None


def get_shorts_progress(story_dir: str) -> Dict[str, Dict[str, bool]]:
    """
    Get the progress status for all shorts in the story directory.

    Args:
        story_dir (str): Path to the main story directory

    Returns:
        Dict[str, Dict[str, bool]]: Progress status for each short
    """
    progress = {}
    short_dirs = get_shorts_directories(story_dir)
    
    for short_dir in short_dirs:
        short_name = os.path.basename(short_dir)
        short_progress = {
            'short_generated': False,
            'segments_generated': False,
            'audio_generated': False,
            'metadata_generated': False
        }
        
        # Check if short.json exists
        if os.path.exists(os.path.join(short_dir, "short.json")):
            short_progress['short_generated'] = True
        
        # Check if segments.json exists
        if os.path.exists(os.path.join(short_dir, "segments.json")):
            short_progress['segments_generated'] = True
        
        # Check if audio files exist
        audio_dir = os.path.join(short_dir, 'audio')
        if os.path.exists(audio_dir) and os.listdir(audio_dir):
            short_progress['audio_generated'] = True
        
        # Check if metadata.json exists
        if os.path.exists(os.path.join(short_dir, "metadata.json")):
            short_progress['metadata_generated'] = True
        
        progress[short_name] = short_progress
    
    return progress


def get_audio_files_for_short(short_dir: str) -> List[str]:
    """
    Get all audio files for a specific short.

    Args:
        short_dir (str): Path to the short directory

    Returns:
        List[str]: List of audio file paths
    """
    audio_dir = os.path.join(short_dir, 'audio')
    audio_files = []
    
    if not os.path.exists(audio_dir):
        return audio_files
    
    for file in os.listdir(audio_dir):
        if file.endswith(('.mp3', '.wav')):
            audio_files.append(os.path.join(audio_dir, file))
    
    # Sort by segment number
    audio_files.sort(key=lambda x: int(x.split('_segment_')[-1].split('.')[0]) if '_segment_' in x else 0)
    
    return audio_files


def count_completed_shorts(story_dir: str) -> Tuple[int, int]:
    """
    Count how many shorts are completed vs total.

    Args:
        story_dir (str): Path to the main story directory

    Returns:
        Tuple[int, int]: (completed_shorts, total_shorts)
    """
    progress = get_shorts_progress(story_dir)
    total_shorts = len(progress)
    completed_shorts = 0
    
    for short_progress in progress.values():
        # Consider a short completed if it has segments and audio
        if short_progress['segments_generated'] and short_progress['audio_generated']:
            completed_shorts += 1
    
    return completed_shorts, total_shorts


def cleanup_incomplete_shorts(story_dir: str) -> None:
    """
    Clean up incomplete short directories (those without short.json).

    Args:
        story_dir (str): Path to the main story directory
    """
    short_dirs = get_shorts_directories(story_dir)
    
    for short_dir in short_dirs:
        short_json_path = os.path.join(short_dir, "short.json")
        
        if not os.path.exists(short_json_path):
            logger.info(f"Removing incomplete short directory: {short_dir}")
            try:
                import shutil
                shutil.rmtree(short_dir)
            except Exception as e:
                logger.error(f"Error removing directory {short_dir}: {str(e)}")


def validate_shorts_structure(story_dir: str) -> bool:
    """
    Validate that the shorts directory structure is correct.

    Args:
        story_dir (str): Path to the main story directory

    Returns:
        bool: True if structure is valid, False otherwise
    """
    short_dirs = get_shorts_directories(story_dir)
    
    if not short_dirs:
        logger.warning("No shorts directories found")
        return False
    
    # Check that short numbers are sequential
    expected_numbers = list(range(1, len(short_dirs) + 1))
    actual_numbers = []
    
    for short_dir in short_dirs:
        try:
            short_num = int(os.path.basename(short_dir).split("short-")[-1])
            actual_numbers.append(short_num)
        except ValueError:
            logger.error(f"Invalid short directory name: {short_dir}")
            return False
    
    actual_numbers.sort()
    
    if actual_numbers != expected_numbers:
        logger.error(f"Short numbers are not sequential. Expected: {expected_numbers}, Got: {actual_numbers}")
        return False
    
    # Check that each short directory has required structure
    for short_dir in short_dirs:
        audio_dir = os.path.join(short_dir, 'audio')
        if not os.path.exists(audio_dir):
            logger.error(f"Missing audio directory in: {short_dir}")
            return False
    
    logger.info(f"Shorts directory structure is valid ({len(short_dirs)} shorts)")
    return True


def get_next_short_to_process(story_dir: str, step: str) -> Optional[str]:
    """
    Get the next short directory that needs processing for a specific step.

    Args:
        story_dir (str): Path to the main story directory
        step (str): Step to check ('segments', 'audio', 'metadata')

    Returns:
        Optional[str]: Path to the next short directory to process, or None if all done
    """
    progress = get_shorts_progress(story_dir)
    
    step_mapping = {
        'segments': 'segments_generated',
        'audio': 'audio_generated',
        'metadata': 'metadata_generated'
    }
    
    if step not in step_mapping:
        logger.error(f"Invalid step: {step}")
        return None
    
    step_key = step_mapping[step]
    
    # Find the first short that hasn't completed this step
    for short_name in sorted(progress.keys()):
        if not progress[short_name][step_key]:
            # For audio and metadata, also check that prerequisites are met
            if step == 'audio' and not progress[short_name]['segments_generated']:
                continue
            if step == 'metadata' and not progress[short_name]['short_generated']:
                continue
            
            return os.path.join(story_dir, short_name)
    
    return None
